# GLua API Snippets Plugin Refactor Plan

_Last updated: 2025-09-09_

## 1. Goals
Make the plugin cohesive and simpler while preserving 100% of existing behavior:
- Keep <PERSON>'s Mod–specific `ResolveRequire` exactly as-is.
- Retain `OnSetText` text-diff approach (debug friendly).
- No change to generated doc text, diff ordering, or detection semantics.
- Remove dead / duplicated / over-engineered code (KISS principle).
- Ensure plan addresses every module (`plugin.lua`, `plugin/defaults.lua`, `plugin/accessor-processor.lua`, `plugin/networkvar-processor.lua`, `plugin/derma-processor.lua`, `plugin/folder-detection.lua`).
- Prefer configuration-driven values over hard-coded literals (patterns, scopes, dt types, force types, base mappings).
- Apply KISS, YAGNI, DRY: only add indirection when it collapses real duplication; avoid speculative extensibility. Avoid usage of comments outside of LuaLS annotations and complex code explanation.

## 2. Hard Constraints (No-Change Zones)
These must not change in logic or output:
- `ResolveRequire` resolution rules & fallback (`relative` then `/lua/`).
- Ordering and content of diffs from `OnSetText`.
- Scripted class detection semantics (`GetScopedClass`, scope matching, base resolution logic).
- Processors' diff formats: AccessorFunc, NetworkVar/NetworkVarElement, Derma (vgui + derma.DefineControl), DEFINE_BASECLASS replacement.
- Existing pattern recognition (still regex-based; no AST migration yet).
- Config merge behavior (`plugin.config.lua` + defaults).
- Folder detection stub behavior (no behavioral change this phase).
- Retain the `debug.getlocal`-based workaround used to extract the actual calling file URI for `ResolveRequire` due to upstream parameter bug / documentation mismatch (see: https://github.com/LuaLS/LuaLS.github.io/issues/48 and related lua-language-server issue). This introspection must remain untouched until the upstream API guarantees the file URI directly.

## 3. Current Issues
| Category | Issue | Impact |
|----------|-------|--------|
| Caching | Custom LRU-esque `CacheManager` adds complexity with negligible benefit | Readability / maintenance cost |
| Legacy Utilities | `PatternUtils`, unused `TextProcessor` helpers linger | Noise & confusion |
| Duplication | Getter/Setter doc line formatting repeated | Risk of drift |
| Indirection | Accessor field docs extracted by re-parsing diff text | Indirect & brittle |
| Clutter | Redundant `PATTERNS` table overlaps config patterns | Increases mental overhead |
| Hard-coded values | Some processors have fallback patterns duplicated (inline regex literals) | Divergence risk |
| Parsing duplication | Accessor & NetworkVar argument splitting logic structurally similar | Maintenance overhead |
| Folder detection stub | Mock always-true `fileExists` & empty directory listing | Misleading semantics |
| Mixed concerns | `plugin.lua` contains caching, detection, diff assembly, and config plumbing | Reduced readability |

## 4. Phase Overview
### Phase 1 – Only-Safe Cleanup (Behavior-Preserving)
Minimal, mechanical removals & simplifications:
1. Remove `CacheManager` & eviction logic; replace with a simple local config cache sentinel in `loadConfig`.
2. Remove `uriExists` cache; inline equivalent filesystem check (retain exact semantics) in `ResolveRequire`.
3. Delete unused `PatternUtils` & `PATTERNS` table (after confirming zero references).
4. Remove unused `TextProcessor` functions: `findNearestPriorAssignment`, `generateAccessorFieldDocs`, `hasClassDocBefore` (if not referenced). Keep `hasExistingClassDoc`.
5. Prune stale comments; add a concise architecture comment header.
6. Keep processors untouched (no output differences risk introduced).
7. Preserve `ResolveRequire` logic verbatim except swapping call to removed helper with inline equivalent.
8. Explicitly preserve the `debug.getlocal` URI acquisition workaround (no refactor / relocation in Phase 1).
9. Centralize pattern access: processors must read exclusively from `config.patterns` (retain existing fallback literals but annotate them for later removal in Phase 2).
10. Add minimal internal utilities section (if needed) only for constants re-export (no logic change) – e.g., a `plugin/constants.lua` returning tables from defaults for single import site (optional if it introduces churn; can defer).

### Phase 2 – Optional Cosmetic (Still Behavior-Preserving)
Only executed if desired after Phase 1 validation:
1. Introduce shared local helper for Set/Get line formatting (must reproduce exact strings & ordering; Accessor retains protected field insertion first).
2. (Optional) Provide structured return path from `AccessorProcessor` (e.g. also returning collected field doc lines) to remove diff re-scan. Keep current external API; only internal usage changes.
3. Minor doc/comment improvements (e.g., list supported patterns once).
4. Introduce shared argument splitting helper used by Accessor & NetworkVar processors (must yield identical part arrays).
5. Replace inline duplicated default regex strings inside processors with a single read from `config.patterns` (remove fallback copies once verified).
6. Light modularization: move scripted class path/scope logic from `plugin.lua` into `plugin/scripted-class.lua` (pure extraction, identical API) to shrink main file.
7. (Optional) Introduce `plugin/doc-lines.lua` exporting helpers for getter/setter/backing-field line formatting; processors import and keep line order.

### Phase 3 – Deferred / Future Improvements (Requires Separate Approval)
_Not in scope for current task; listed for roadmap completeness._
1. Real folder detection (using `bee.filesystem`) behind a config flag.
2. AST-based call detection (guide-based) moved to `OnTransformAst` for multiline robustness.
3. Snapshot test harness for diff outputs (Jest or Lua script) to lock behavior.
4. Optional removal or integration of manual scope heuristics with workspace APIs.
5. Real folder detection implementation (`bee.filesystem`) behind config `enableFolderDetection` default false.
6. Conditional removal of `debug.getlocal` introspection once upstream bug fixed and version-gated.
7. Enhanced resilience: multi-line call parsing via a simplified token scanner (still pre-AST) if AST migration deferred.

## 5. Detailed Phase 1 Action List
| ID | Action | Safety Notes |
|----|--------|--------------|
| A1 | Remove `CacheManager`; introduce `local configCache = nil` inside `loadConfig` | No external references to cache API promised |
| A2 | Replace `uriExists` usage with local inline existence check (`uri:sub(8)` + `fs.exists`) | Matches current logic byte-for-byte |
| A3 | Delete unused TextProcessor helpers | Confirm with search before deletion |
| A4 | Remove `PatternUtils` & `PATTERNS` table | Ensure processors supply all needed functionality |
| A5 | Remove accessor field doc reconstruction function not used | Reduces dead code |
| A6 | Update file header comments to reflect simplified structure | Non-functional |
| A7 | Keep `ResolveRequire` logic; only swap helper call | Output identical |
| A8 | Preserve `debug.getlocal` workaround usage | Upstream bug dependency; prevents accidental removal |
| A9 | Annotate processors to clarify pattern source is config and mark fallback duplicates | No runtime change |
| A10 | (Optional) Add constants module re-exporting defaults | Must not rename existing keys |

## 6. Verification Strategy
| Step | Purpose | Method |
|------|---------|--------|
| V1 | Baseline capture (optional) | Log / serialize diffs for representative test files pre-change |
| V2 | Apply Phase 1 | Implement A1–A7 |
| V3 | Post-change capture | Repeat diff collection; compare JSON objects (`start`, `finish`, `text`) |
| V4 | Symbol purge check | Grep for removed identifiers (`CacheManager`, `PatternUtils`, etc.) |
| V5 | Syntax sanity | `require` plugin entry (or Lua parse) to ensure no syntax errors |
| V6 | `ResolveRequire` parity | Test with sample paths; confirm identical returned tables |
| V7 | Multi-file parity | For each processor module, run identical input samples pre/post; diff serialized outputs |
| V8 | Pattern indirection safety | Temporarily change a pattern in config and confirm processors reflect change without code modification |
| V9 | Scripted class extraction unchanged | Feed identical file paths & contents; verify chosen class/global/base tuple stable |

### Representative Fixture Suggestions
- Scripted class with AccessorFuncs + NetworkVars
- File with `DEFINE_BASECLASS`
- File registering both `vgui.Register` and `derma.DefineControl`
- File already containing an existing `---@class` doc (idempotence check)
- File adjusting a single pattern in config (e.g., tighten `AccessorFunc` regex) to verify dynamic pattern usage.
- NetworkVarElement with index to verify untouched formatting.
- AccessorFunc with FORCE_* numeric & named constants to ensure mapping stability.

## 7. Acceptance Criteria
- Diff arrays for unchanged files are byte-identical pre/post Phase 1.
- No new diagnostics or runtime errors when plugin loads.
- `ResolveRequire` returns same candidate list (order preserved).
- All removed identifiers absent from codebase.
- No changes to whitespace or ordering of generated annotation lines.
- Pattern change in config (without code edits) immediately affects detection (after Phase 2 if fallback literals removed).
- Argument splitting helper (Phase 2) produces identical tokens vs legacy logic (validated by snapshot).

## 8. Rollback Plan
If any discrepancy appears in V3 comparison:
1. Revert the single related commit (Phase 1 changes should be an isolated commit).
2. Re-run baseline capture to validate restoration.
3. Refine change (split into even smaller atomic edits) and retest.

## 9. Risk Assessment
| Risk | Likelihood | Impact | Mitigation |
|------|------------|--------|------------|
| Hidden dependency on `PatternUtils` | Low | Medium | Pre-removal search validation |
| Missed reference to cache functions | Low | Low | Grep after removal |
| Accidental whitespace change in diffs | Low | Medium | Snapshot comparison |
| Future maintenance drift without helper consolidation | Medium | Low | Address in Phase 2 |
| Upstream fix changes `ResolveRequire` first parameter semantics | Medium | Medium | Add detection & conditional path in a future phase before removing workaround |
| Over-extraction churn (too many micro-modules) | Low | Low | Limit Phase 2 modularization to high-cohesion groups only |
| Pattern centralization oversight | Low | Medium | Maintain fallback until tests confirm; remove in a separate commit |
| Shared splitter introduces subtle parsing difference | Medium | Medium | Snapshot test before replacing legacy splitters |

## 10. Deferred Ideas (Reference Only)
- Configurable flag: `enableFolderDetection`.
- AST-first transformation path to reduce regex dependency.
- Inline performance metrics (optional debug flag).
- Conditional removal (or feature-flagging) of the `debug.getlocal` workaround once the upstream `ResolveRequire` parameter reliably supplies the file URI (issues #48 / lua-language-server#3259).
- Shared mini tokenizer to unify Accessor/NetworkVar parsing without full AST.
- Config schema validation (lightweight) to warn on unknown pattern keys.

## 11. Implementation Notes
- Keep changes to a single file (`plugin.lua`) in Phase 1 unless dead code spans additional files.
- Do not rename existing public symbols referenced externally.
- Avoid introducing new dependencies; leverage existing `bee.filesystem` and LuaLS modules only if already required.
- When extracting modules (Phase 2), keep file-level return tables named consistently (`ScriptedClass`, `DocLines`, etc.) and update only local requires.
- Each extraction commit should include: (1) pure cut/paste move, (2) follow-up removal of old block, (3) verification snapshot (manual or automated). Separate commits ease rollback.
- Keep fallbacks for patterns until a config-driven override is manually tested; only then remove duplicates.

### 11.1 Modularization & Config Usage Strategy (Detailed)
| Concern | Current Location | Future Location (Phase) | Rationale |
|---------|------------------|--------------------------|-----------|
| Scripted class scope/path logic | `plugin.lua` mixed with diff wiring | `plugin/scripted-class.lua` (Phase 2) | Isolates path heuristics, shrinks core file |
| Getter/Setter/backing field line formatting | Repeated inline in processors | `plugin/doc-lines.lua` (Phase 2 optional) | Single source; easier type formatting tweaks |
| Arg splitting (Accessor, NetworkVar, NetworkVarElement) | Separate custom loops | `plugin/arg-split.lua` (Phase 2) | DRY; unified bug fixes |
| Pattern source of truth | `defaults.lua` + inline fallbacks | `defaults.lua` only (Phase 2 removal of fallbacks) | Avoid divergence |
| Potential constants (dtTypes, accessorForceTypes) | `defaults.lua` consumed directly | (No change; optional `constants.lua` façade if import noise) | Unnecessary abstraction avoided (YAGNI) |
| Folder detection enhanced logic | Stub in `folder-detection.lua` | Same file with real implementation guarded by config flag (Phase 3) | Contained feature toggle |

Guidelines:
- Do not introduce more than 1 new file per sub-concern in a single commit.
- Each extraction must be a pure textual move first (no edits) followed by a refactor commit if needed.
- All new helper modules return plain tables; no side effects at load time.
- Configuration access always goes through a single `loadConfig()` or passed `config` argument; helpers never read disk or global state.

---
**Ready for Execution:** Confirm to proceed with Phase 1 implementation or request adjustments.

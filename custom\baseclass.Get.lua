---![(Shared and <PERSON><PERSON>)](https://github.com/user-attachments/assets/8f5230ff-38f7-493b-b9fc-cc70ffd5b3f4) Gets the base class of an an object.
---
--- This is used not just by entities, but also by widgets, panels, drive modes, weapons and gamemodes (with "gamemode_" prefix).
---
--- The keyword **DEFINE_BASECLASS** translates into a call to this function. In the engine, it is replaced with:
---
--- ```lua
--- local BaseClass = baseclass.Get
--- ```
---
--- **NOTE**: You should prefer using this instead of `self.BaseClass` to avoid infinite recursion.
---
--- For more information, including usage examples, see the [BaseClasses](https://wiki.facepunch.com/gmod/BaseClasses) reference page.
---
---[View wiki](https://wiki.facepunch.com/gmod/baseclass.Get)
---@generic T
---@param name `T` The child class.
---@return `T` # The base class's meta table.
function baseclass.Get(name) end

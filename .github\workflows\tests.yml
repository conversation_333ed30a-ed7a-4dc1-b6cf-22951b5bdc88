name: tests
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - "**"
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: "22"
      - name: Install dependencies
        run: npm ci
      - name: Run tests
        run: npm run ci:test
      - uses: coverallsapp/github-action@v2

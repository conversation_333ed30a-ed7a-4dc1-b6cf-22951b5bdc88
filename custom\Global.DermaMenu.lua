---![(Client and menu)](https://github.com/user-attachments/assets/25d1a1c8-4288-4a51-9867-5e3bb51b9981) Creates a DMenu and closes any current menus.
---
---[View wiki](https://wiki.facepunch.com/gmod/Global.DermaMenu)
---@param keepOpen? boolean If we should keep other DMenus open (`true`) or not (`false`).
---@param parent? Panel The panel to parent the created menu to.
---@return DMenu #The created DMenu.
function _G.DermaMenu(keepOpen, parent) end

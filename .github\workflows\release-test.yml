name: release-test
on:
  workflow_dispatch:
  push:
    branches:
      #- main
      - test-plugin
  #schedule:
    #- cron: "0 0 1 * *"
jobs:
  release:
    permissions: write-all
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v3
        with:
          node-version: "22"
      - name: Install dependencies
        run: npm ci
      - name: Check if there have been changes to the wiki
        id: wiki_confirm_no_changes
        continue-on-error: true
        run: |
          if [ "$RUNNER_DEBUG" = "1" ]; then
            exit 1
          else
            # always pass a value even if there are no tags
            tags=$(git tag -l --sort=-v:refname)
            npm run wiki-check-changed "$tags"
          fi
      - name: Install zip
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        uses: montudor/action-zip@v1
      - name: Scrape wiki
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        run: npm run scrape-wiki
      - name: Format the output with StyLua
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        uses: JohnnyMorganz/stylua-action@v2.0.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          version: latest
          args: --no-editorconfig output/
      - name: Pack release
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        run: npm run pack-release
      - name: Publish Lua Language Server Library
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        run: npm run publish-library
      - name: Get release files and version
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        id: release_json
        run: |
          content=`cat ./dist/release/release.json`
          # the following lines are only required for multi line json
          content="${content//'%'/'%25'}"
          content="${content//$'\n'/'%0A'}"
          content="${content//$'\r'/'%0D'}"
          # end of optional handling for multi line json
          echo "releaseJson<<EOF" >> $GITHUB_OUTPUT
          echo "${content}" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
      - name: Generate next tag
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        id: next_tag
        run: |
          # Ensure we have remote tags
          git fetch --tags origin || true

          # Find latest vNNNN tag locally (if any)
          lastTag=$(git tag -l --sort=-v:refname | grep -E "^v[0-9]{4}$" | head -n 1 || true)
          if [ -z "$lastTag" ]; then
            candidate=1
          else
            lastNum=${lastTag:1}
            candidate=$(( 10#$lastNum + 1 ))
          fi

          # Loop until we find a tag that does not exist locally nor on the remote
          while true; do
            newTag=$(printf "v%04d" $candidate)

            # check remote
            if git ls-remote --exit-code --tags origin "$newTag" >/dev/null 2>&1; then
              candidate=$((candidate+1))
              continue
            fi

            # check local
            if git rev-parse -q --verify "refs/tags/$newTag" >/dev/null 2>&1; then
              candidate=$((candidate+1))
              continue
            fi

            break
          done

          echo "newTag=$newTag" >> $GITHUB_OUTPUT
      - name: Create plugin asset
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        id: create_plugin_asset
        run: |
          mkdir -p dist/release
          releaseFiles=$(node -e "const r=require('./dist/release/release.json'); process.stdout.write((r.releaseFiles||[]).join(','))")
          version="${{ fromJson(steps.release_json.outputs.releaseJson).version }}"

          # Derive a safe base name: prefer first release file stem, else sanitize version
          if [ -n "$releaseFiles" ]; then
            firstFile=$(echo "$releaseFiles" | cut -d',' -f1)
            baseName=$(basename "$firstFile")
            baseName=${baseName%%.*} # strip extension parts (.json.zip/.lua.zip)
          else
            baseName=$(echo "$version" | tr ' /:' '__-' | sed 's/[^A-Za-z0-9._-]/_/g')
          fi

          plugin_file="dist/release/${baseName}.plugin.zip"

          if [ -d dist/libraries/garrysmod ]; then
            (cd dist/libraries/garrysmod && zip -qr "$(pwd)/../../release/${baseName}.plugin.zip" .)
          fi

          if [ -f "$plugin_file" ]; then
            if [ -z "$releaseFiles" ]; then
              combined="$plugin_file"
            else
              combined="$releaseFiles,$plugin_file"
            fi
          else
            combined="$releaseFiles"
            echo "Plugin file was not created; continuing without it." >&2
          fi

          combined=$(echo "$combined" | tr -d '\n' | sed 's/ //g')
          echo "Artifacts to upload: $combined"
          [ -z "$combined" ] && echo "No artifacts detected for release." >&2
          echo "releaseArtifacts=$combined" >> $GITHUB_OUTPUT
      - name: Create release
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        id: create_release
        uses: ncipollo/release-action@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          name: ${{ fromJson(steps.release_json.outputs.releaseJson).version }}
          tag: ${{ steps.next_tag.outputs.newTag }}
          prerelease: ${{ startsWith(github.ref_name, 'test') }}
          body: |
            # 📅 ${{ fromJson(steps.release_json.outputs.releaseJson).version }}
            This release was automatically generated by [this GitHub Action Workflow](${{ github.server_url }}/${{ github.repository }}/blob/${{ github.ref_name }}/.github/workflows/release-test.yml).

            ## 📝 Attached Archives
            With this release the following archives are attached:
            - "*.lua.zip": Annotations for Lua definitions to include in your workspace (e.g. EmmyLua)
            - "*.json.zip": JSON definitions based on the Wiki contents (can be used for further processing)
            - "*.plugin.zip": Plugin archive containing the library files for this release (packaged from the lua-language-server-addon branch contents).

          artifacts: ${{ steps.create_plugin_asset.outputs.releaseArtifacts }}
      - name: Copy library to it's own branch
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        env:
          LIBRARY_PATH: dist/libraries/garrysmod
          TARGET_BRANCH: lua-language-server-addon
          NEW_TAG: ${{ steps.next_tag.outputs.newTag }}
        run: |
          set -e
          git config --global user.name 'GitHub Action'
          git config --global user.email '<EMAIL>'

          # Top-level directory we should preserve ("dist") so we don't delete the library before copying
          libraryPathFirstPart="$(echo "$LIBRARY_PATH" | cut -d'/' -f1)"

          if [ ! -d "$LIBRARY_PATH" ] || [ -z "$(ls -A "$LIBRARY_PATH" 2>/dev/null)" ]; then
            echo "No library files found at $LIBRARY_PATH — skipping branch publish."
            exit 0
          fi

          git fetch origin --prune || true
          # Checkout (create if needed) target branch
          git checkout $TARGET_BRANCH 2>/dev/null || git checkout -b $TARGET_BRANCH

          # Remove everything except .git and the preserved top directory containing our library
          # (mirrors logic in original upstream workflow)
          toRemove=$(ls -A | grep -vE ".git$|^${libraryPathFirstPart}$") || true
          if [ -n "$toRemove" ]; then
            echo "Removing old branch contents (except ${libraryPathFirstPart})"
            rm -rf $toRemove
          fi

          # Move library contents into branch root
            mv "$LIBRARY_PATH"/* .
          # Clean up leftover dist directory (we only need raw library files in branch)
          rm -rf "$libraryPathFirstPart"

          git add -A
          if ! git diff-index --quiet HEAD; then
            git commit -m "🚀 Update ${{ fromJson(steps.release_json.outputs.releaseJson).version }}" || true
          else
            echo "No changes to commit."; exit 0
          fi

          # Tag if it doesn't already exist
          if ! git rev-parse -q --verify "refs/tags/$NEW_TAG" >/dev/null 2>&1; then
            git tag "$NEW_TAG" || true
          else
            echo "Tag $NEW_TAG already exists; skipping tag creation."
          fi

          git push origin $TARGET_BRANCH || true
          git push origin "$NEW_TAG" || true

        # NOTE: From here on the workflow repo no longer contains the dist files, nor node_modules (removed when publishing branch)

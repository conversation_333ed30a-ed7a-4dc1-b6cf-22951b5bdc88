{"name": "glua-api-scraper", "description": "Scrapes the Garry's Mod Wiki in order to build annotations for Lua Language Server to provide intellisense.", "keywords": ["Garry's Mod", "api", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Language Server"], "homepage": "https://github.com/luttje/glua-api-snippets", "repository": {"type": "git", "url": "https://github.com/luttje/glua-api-snippets"}, "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "version": "0.2.0", "type": "module", "scripts": {"clear-output": "rm -rf ./output/", "wiki-check-changed": "tsx ./src/cli-change-checker.ts", "scrape-wiki": "tsx ./src/cli-scraper.ts --output ./output/ --customOverrides ./custom/ --wipe", "pack-release": "tsx ./src/cli-release-packer.ts --input ./output/ --output ./dist/release/", "publish-library": "tsx ./src/cli-library-publisher.ts --input ./output/ --output ./dist/libraries/garrysmod", "stylua-custom": "npx --yes @johnnymorganz/stylua-bin@0.17.1 ./custom", "test": "cross-env NODE_OPTIONS=--experimental-vm-modules VERBOSE_LOGGING=1 npx jest", "ci:test": "cross-env NODE_OPTIONS=--experimental-vm-modules npx jest --ci --coverage"}, "devDependencies": {"@types/archiver": "^6.0.2", "@types/jest": "^29.5.11", "@types/node": "^20.19.10", "archiver": "^6.0.1", "cheerio": "^1.0.0-rc.12", "cross-env": "^7.0.3", "fetch-retry": "^5.0.6", "html-loader": "^4.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-fetch-mock": "^3.0.3", "node-stream-zip": "^1.15.0", "reflect-metadata": "^0.2.1", "ts-jest": "^29.1.1", "ts-jest-resolver": "^2.0.1", "ts-node": "^10.9.2", "tsx": "^4.7.0", "typescript": "^5.3.3"}, "gitHead": "2513190cc1de00d1049e35f9c942b779447530f9", "engines": {"node": ">=21.0.0"}, "engineStrict": true, "dependencies": {"commander": "^14.0.0"}}
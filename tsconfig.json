{"compilerOptions": {"module": "ESNext", "moduleResolution": "node", "target": "ES2016", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "noImplicitAny": true, "removeComments": true, "preserveConstEnums": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "sourceMap": true, "allowJs": true, "outDir": "dist/esm", "declaration": true, "declarationDir": "dist/types"}, "include": ["src/**/*"]}
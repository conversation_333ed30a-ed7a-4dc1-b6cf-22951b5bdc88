name: release
on:
  workflow_dispatch:
  push:
    branches:
      - main
  schedule:
    - cron: "0 0 1 * *"
jobs:
  release:
    permissions: write-all
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v3
        with:
          node-version: "22"
      - name: Install dependencies
        run: npm ci
      - name: Check if there have been changes to the wiki
        id: wiki_confirm_no_changes
        continue-on-error: true
        run: |
          if [ "$RUNNER_DEBUG" = "1" ]; then
            exit 1
          else
            npm run wiki-check-changed $(git tag -l --sort=-v:refname)
          fi
      - name: Install zip
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        uses: montudor/action-zip@v1
      - name: Scrape wiki
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        run: npm run scrape-wiki
      - name: Format the output with StyLua
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        uses: JohnnyMorganz/stylua-action@v2.0.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          version: latest
          args: --no-editorconfig output/
      - name: Pack release
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        run: npm run pack-release
      - name: Get release files and version
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        id: release_json
        run: |
          content=`cat ./dist/release/release.json`
          # the following lines are only required for multi line json
          content="${content//'%'/'%25'}"
          content="${content//$'\n'/'%0A'}"
          content="${content//$'\r'/'%0D'}"
          # end of optional handling for multi line json
          echo "releaseJson=$content" >> $GITHUB_ENV
      - name: Create release
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        id: create_release
        uses: ncipollo/release-action@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          name: ${{ fromJson(env.releaseJson).version }}
          tag: ${{ fromJson(env.releaseJson).tag }}
          body: |
            # 📅 ${{ fromJson(env.releaseJson).version }}
            This release was automatically generated by [this GitHub Action Workflow](https://github.com/luttje/glua-api-snippets/blob/main/.github/workflows/release.yml).

            ## 📝 Attached Archives
            With this release the following archives are attached:
            - `*.lua.zip`: Annotations for Lua definitions to include in your workspace (e.g: for EmmyLua)
            - `*.json.zip`: JSON definitions based on the Wiki contents (can be used for further processing)

            ## 📚 Lua Language Server Addon
            With this release a new addon for [the Lua Language Server](https://github.com/LuaLS/lua-language-server) was made available in [the LLS-Addons repository](https://github.com/LuaLS/LLS-Addons).
            The source of this release can be found in [the lua-language-server-addon branch](https://github.com/luttje/glua-api-snippets/tree/lua-language-server-addon).

          artifacts: ${{ join(fromJson(env.releaseJson).releaseFiles, ',') }}
      - name: Publish Lua Language Server Library
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        run: npm run publish-library
      - name: Copy library to it's own branch
        if: ${{ steps.wiki_confirm_no_changes.outcome == 'failure' }}
        env:
          LIBRARY_PATH: dist/libraries/garrysmod
          TARGET_BRANCH: lua-language-server-addon
        run: |
          libraryPathFirstPart=`echo $LIBRARY_PATH | cut -d'/' -f1`

          git config --global user.name 'GitHub Action'
          git config --global user.email '<EMAIL>'

          # Checkout the target branch, create it if it doesn't exist
          git checkout $TARGET_BRANCH 2>/dev/null || git checkout -b $TARGET_BRANCH

          # Remove all files except .git and the library files
          rm -rf $(ls -A | grep -vE ".git$|^$libraryPathFirstPart$")
          mv $LIBRARY_PATH/* .
          rm -rf $libraryPathFirstPart

          # Commit these changes
          git add -A
          git diff-index --quiet HEAD || git commit -am "🚀 Update ${{ fromJson(env.releaseJson).version }}"

          # Increment the tag, based on the previous tag (v0001, v0002, ...)
          lastTag=$(git tag -l --sort=-v:refname | grep -E "^v[0-9]{4}$" | head -n 1)
          lastTagNumber=${lastTag:1}
          # Force base 10 for numbers, even if they lead with zero (which would normally be considered octal)
          newTagNumber=$(( 10#$lastTagNumber + 1 ))
          newTag="v$(printf "%04d" $newTagNumber)"
          git tag $newTag

          # Push the changes (including the tag)
          git push origin $TARGET_BRANCH
          git push origin $newTag

        # NOTE: From here on the workflow repo no longer contains the dist files, nor node_modules (removed when publishing branch)

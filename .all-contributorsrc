{"projectName": "glua-api-snippets", "projectOwner": "lutt<PERSON>", "files": ["README.md"], "commitType": "docs", "commitConvention": "angular", "contributorsPerLine": 7, "contributors": [{"login": "lutt<PERSON>", "name": "lutt<PERSON>", "avatar_url": "https://avatars.githubusercontent.com/u/2738114?v=4", "profile": "https://github.com/luttje", "contributions": ["code"]}, {"login": "aske02", "name": "<PERSON><PERSON>", "avatar_url": "https://avatars.githubusercontent.com/u/45128441?v=4", "profile": "https://github.com/aske02", "contributions": ["code"]}, {"login": "Vurv78", "name": "<PERSON><PERSON><PERSON>", "avatar_url": "https://avatars.githubusercontent.com/u/56230599?v=4", "profile": "https://vurv78.github.io/", "contributions": ["code"]}, {"login": "yogwoggf", "name": "jason", "avatar_url": "https://avatars.githubusercontent.com/u/100450992?v=4", "profile": "https://github.com/yogwoggf", "contributions": ["ideas"]}, {"login": "AMD-NICK", "name": "_AMD_", "avatar_url": "https://avatars.githubusercontent.com/u/9200174?v=4", "profile": "https://blog.amd-nick.me", "contributions": ["bug"]}, {"login": "b0mbie", "name": "[aka]bomb", "avatar_url": "https://avatars.githubusercontent.com/u/69766525?v=4", "profile": "https://github.com/b0mbie", "contributions": ["bug", "code"]}, {"login": "robotboy655", "name": "<PERSON><PERSON><PERSON>", "avatar_url": "https://avatars.githubusercontent.com/u/3299036?v=4", "profile": "http://steamcommunity.com/id/Robotboy655", "contributions": ["code"]}, {"login": "Techbot121", "name": "Techbot121", "avatar_url": "https://avatars.githubusercontent.com/u/3000604?v=4", "profile": "https://github.com/Techbot121", "contributions": ["bug"]}, {"login": "TIMONz1535", "name": "TIMON_Z1535", "avatar_url": "https://avatars.githubusercontent.com/u/7351599?v=4", "profile": "https://github.com/TIMONz1535", "contributions": ["ideas", "bug"]}, {"login": "Cynosphere", "name": "<PERSON>", "avatar_url": "https://avatars.githubusercontent.com/u/1606710?v=4", "profile": "https://c7.pm", "contributions": ["doc"]}]}